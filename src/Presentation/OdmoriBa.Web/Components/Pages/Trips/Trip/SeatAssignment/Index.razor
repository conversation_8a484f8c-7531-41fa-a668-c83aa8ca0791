@page "/trips/{TripId:guid}/seat-assignment"
@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Application.Features.Trips.Commands
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Domains.Trips.Entities

@implements IDisposable

@inject IDialogService DialogService
@inject TripState TripState
@inject IMediator Mediator
@inject ISnackbar Snackbar

<PageTitle>Putovanje | Raspored sjedišta</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Putovanja", href: "/trips", icon: Icons.Material.Filled.CardTravel),
                   new BreadcrumbItem($"{_trip?.Title} ({_trip?.DateRange})", href: $"/trips/{_trip?.Id!}", icon: _trip?.TransportationType == TransportationType.Bus ? Icons.Material.Filled.DirectionsBus : Icons.Material.Filled.AirplanemodeActive),
                   new BreadcrumbItem("Raspored sjedišta", href: $"/trips/{_trip?.Id!}/seat-assignment", icon: Icons.Material.Filled.EventSeat),
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>

    <MudButton StartIcon="@Icons.Material.Filled.Save"
               OnClick="@SaveSeatAssignmentsAsync"
               Variant="Variant.Filled"
               Color="Color.Primary"
               Disabled="@(!_hasChanges)">
        Sačuvaj raspored
    </MudButton>
</MudToolBar>

<MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
    <MudTabPanel Text="Polazak" Icon="@Icons.Material.Filled.FlightTakeoff">
        @if (_trip?.TripBuses.Any(b => b.Direction == TripBusDirection.Departure) == true)
        {
            <MudGrid>
                <MudItem md="4">
                    <MudPaper Class="pa-4" Elevation="2">
                        <MudText Typo="Typo.h6" Class="mb-4">Putnici</MudText>
                        @foreach (var travelParty in _travelParties)
                        {
                            <MudExpansionPanels Elevation="1" Class="mb-2">
                                <MudExpansionPanel Text="@($"{travelParty.MainContact?.FullName} ({travelParty.Travelers.Count} putnika)")">
                                    <TitleContent>
                                        <div style="display: flex; align-items: center">
                                            <MudIcon Icon="@Icons.Material.Filled.Group" class="mr-3"></MudIcon>
                                            <div>
                                                <MudText>@travelParty.MainContact?.FullName</MudText>
                                                <MudText Typo="Typo.body2" Class="mud-text-secondary">@travelParty.Travelers.Count putnika</MudText>
                                            </div>
                                            <MudSpacer/>
                                            <MudButton Size="Size.Small" 
                                                      Variant="Variant.Outlined" 
                                                      Color="Color.Primary"
                                                      OnClick="@(() => SelectTravelParty(travelParty, TripBusDirection.Departure))"
                                                      Class="mr-2">
                                                Odaberi grupu
                                            </MudButton>
                                        </div>
                                    </TitleContent>
                                    <ChildContent>
                                        @foreach (var traveler in travelParty.Travelers)
                                        {
                                            <MudCard Class="@($"mb-2 {(IsSelected(traveler, TripBusDirection.Departure) ? "mud-theme-primary" : "")}")"
                                                     Style="cursor: pointer"
                                                     @onclick="@(() => SelectTraveler(traveler, TripBusDirection.Departure))">
                                                <MudCardContent Class="pa-2">
                                                    <MudText Typo="Typo.body1">@traveler.Person?.FullName</MudText>
                                                    @if (traveler.DepartureTripBusId.HasValue && traveler.DepartureSeatNumber.HasValue)
                                                    {
                                                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                                            @GetBusName(traveler.DepartureTripBusId.Value) - Sjedište @traveler.DepartureSeatNumber
                                                        </MudText>
                                                    }
                                                    else
                                                    {
                                                        <MudText Typo="Typo.body2" Class="mud-text-secondary">Nije dodijeljeno</MudText>
                                                    }
                                                </MudCardContent>
                                            </MudCard>
                                        }
                                    </ChildContent>
                                </MudExpansionPanel>
                            </MudExpansionPanels>
                        }
                    </MudPaper>
                </MudItem>
                <MudItem md="8">
                    <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true">
                        @foreach (var bus in _trip.TripBuses.Where(b => b.Direction == TripBusDirection.Departure))
                        {
                            <MudTabPanel Text="@bus.Name" Icon="@Icons.Material.Filled.DirectionsBus">
                                <div class="pa-4">
                                    <MudText Typo="Typo.h6" Class="mb-4">@bus.Bus?.FullName</MudText>
                                    @RenderBusSeats(bus, TripBusDirection.Departure)
                                </div>
                            </MudTabPanel>
                        }
                    </MudTabs>
                </MudItem>
            </MudGrid>
        }
        else
        {
            <MudAlert Severity="Severity.Info">Nema autobusa za polazak.</MudAlert>
        }
    </MudTabPanel>

    <MudTabPanel Text="Povratak" Icon="@Icons.Material.Filled.FlightLand">
        @if (_trip?.TripBuses.Any(b => b.Direction == TripBusDirection.Return) == true)
        {
            <MudGrid>
                <MudItem md="4">
                    <MudPaper Class="pa-4" Elevation="2">
                        <MudText Typo="Typo.h6" Class="mb-4">Putnici</MudText>
                        @foreach (var travelParty in _travelParties)
                        {
                            <MudExpansionPanels Elevation="1" Class="mb-2">
                                <MudExpansionPanel Text="@($"{travelParty.MainContact?.FullName} ({travelParty.Travelers.Count} putnika)")">
                                    <TitleContent>
                                        <div style="display: flex; align-items: center">
                                            <MudIcon Icon="@Icons.Material.Filled.Group" class="mr-3"></MudIcon>
                                            <div>
                                                <MudText>@travelParty.MainContact?.FullName</MudText>
                                                <MudText Typo="Typo.body2" Class="mud-text-secondary">@travelParty.Travelers.Count putnika</MudText>
                                            </div>
                                            <MudSpacer/>
                                            <MudButton Size="Size.Small" 
                                                      Variant="Variant.Outlined" 
                                                      Color="Color.Primary"
                                                      OnClick="@(() => SelectTravelParty(travelParty, TripBusDirection.Return))"
                                                      Class="mr-2">
                                                Odaberi grupu
                                            </MudButton>
                                        </div>
                                    </TitleContent>
                                    <ChildContent>
                                        @foreach (var traveler in travelParty.Travelers)
                                        {
                                            <MudCard Class="@($"mb-2 {(IsSelected(traveler, TripBusDirection.Return) ? "mud-theme-primary" : "")}")"
                                                     Style="cursor: pointer"
                                                     @onclick="@(() => SelectTraveler(traveler, TripBusDirection.Return))">
                                                <MudCardContent Class="pa-2">
                                                    <MudText Typo="Typo.body1">@traveler.Person?.FullName</MudText>
                                                    @if (traveler.ReturnTripBusId.HasValue && traveler.ReturnSeatNumber.HasValue)
                                                    {
                                                        <MudText Typo="Typo.body2" Class="mud-text-secondary">
                                                            @GetBusName(traveler.ReturnTripBusId.Value) - Sjedište @traveler.ReturnSeatNumber
                                                        </MudText>
                                                    }
                                                    else
                                                    {
                                                        <MudText Typo="Typo.body2" Class="mud-text-secondary">Nije dodijeljeno</MudText>
                                                    }
                                                </MudCardContent>
                                            </MudCard>
                                        }
                                    </ChildContent>
                                </MudExpansionPanel>
                            </MudExpansionPanels>
                        }
                    </MudPaper>
                </MudItem>
                <MudItem md="8">
                    <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true">
                        @foreach (var bus in _trip.TripBuses.Where(b => b.Direction == TripBusDirection.Return))
                        {
                            <MudTabPanel Text="@bus.Name" Icon="@Icons.Material.Filled.DirectionsBus">
                                <div class="pa-4">
                                    <MudText Typo="Typo.h6" Class="mb-4">@bus.Bus?.FullName</MudText>
                                    @RenderBusSeats(bus, TripBusDirection.Return)
                                </div>
                            </MudTabPanel>
                        }
                    </MudTabs>
                </MudItem>
            </MudGrid>
        }
        else
        {
            <MudAlert Severity="Severity.Info">Nema autobusa za povratak.</MudAlert>
        }
    </MudTabPanel>
</MudTabs>

@code {
    [Parameter] public Guid TripId { get; set; }

    private TripDto? _trip = new();
    private List<TravelPartyDto> _travelParties = [];
    private List<TravelerDto> _selectedTravelers = [];
    private TripBusDirection _currentDirection = TripBusDirection.Departure;
    private bool _hasChanges = false;

    protected override async Task OnParametersSetAsync()
    {
        _trip = await TripState.LoadTripAsync(TripId);
        _travelParties = await TripState.LoadTravelPartiesAsync(TripId);
    }

    protected override void OnInitialized()
    {
        TripState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        TripState.OnChange -= StateHasChanged;
    }

    private void SelectTraveler(TravelerDto traveler, TripBusDirection direction)
    {
        _currentDirection = direction;
        _selectedTravelers.Clear();
        _selectedTravelers.Add(traveler);
        StateHasChanged();
    }

    private void SelectTravelParty(TravelPartyDto travelParty, TripBusDirection direction)
    {
        _currentDirection = direction;
        _selectedTravelers.Clear();
        _selectedTravelers.AddRange(travelParty.Travelers);
        StateHasChanged();
    }

    private bool IsSelected(TravelerDto traveler, TripBusDirection direction)
    {
        return _currentDirection == direction && _selectedTravelers.Contains(traveler);
    }

    private string GetBusName(Guid busId)
    {
        var bus = _trip?.TripBuses.FirstOrDefault(b => b.Id == busId);
        return bus?.Name ?? "Nepoznat autobus";
    }

    private void AssignSeat(TripBusDto bus, int seatNumber, TripBusDirection direction)
    {
        if (!_selectedTravelers.Any()) return;

        var traveler = _selectedTravelers.First();

        if (direction == TripBusDirection.Departure)
        {
            traveler.DepartureTripBusId = bus.Id;
            traveler.DepartureSeatNumber = seatNumber;
        }
        else
        {
            traveler.ReturnTripBusId = bus.Id;
            traveler.ReturnSeatNumber = seatNumber;
        }

        _selectedTravelers.RemoveAt(0);
        _hasChanges = true;
        StateHasChanged();
    }

    private bool IsSeatOccupied(TripBusDto bus, int seatNumber, TripBusDirection direction)
    {
        var allTravelers = _travelParties.SelectMany(tp => tp.Travelers);

        if (direction == TripBusDirection.Departure)
        {
            return allTravelers.Any(t => t.DepartureTripBusId == bus.Id && t.DepartureSeatNumber == seatNumber);
        }
        else
        {
            return allTravelers.Any(t => t.ReturnTripBusId == bus.Id && t.ReturnSeatNumber == seatNumber);
        }
    }

    private TravelerDto? GetTravelerInSeat(TripBusDto bus, int seatNumber, TripBusDirection direction)
    {
        var allTravelers = _travelParties.SelectMany(tp => tp.Travelers);

        if (direction == TripBusDirection.Departure)
        {
            return allTravelers.FirstOrDefault(t => t.DepartureTripBusId == bus.Id && t.DepartureSeatNumber == seatNumber);
        }
        else
        {
            return allTravelers.FirstOrDefault(t => t.ReturnTripBusId == bus.Id && t.ReturnSeatNumber == seatNumber);
        }
    }

    private async Task SaveSeatAssignmentsAsync()
    {
        var assignments = new List<TravelerSeatAssignment>();

        foreach (var travelParty in _travelParties)
        {
            foreach (var traveler in travelParty.Travelers)
            {
                assignments.Add(new TravelerSeatAssignment(
                    traveler.Id,
                    traveler.TravelPartyId,
                    traveler.DepartureTripBusId,
                    traveler.DepartureSeatNumber,
                    traveler.ReturnTripBusId,
                    traveler.ReturnSeatNumber));
            }
        }

        var command = new UpdateTravelersSeatAssignmentsCommand(assignments);
        var result = await Mediator.Send(command);

        result.Switch(
            () =>
            {
                Snackbar.Add("Raspored sjedišta je uspješno sačuvan!", Severity.Success);
                _hasChanges = false;
            },
            error => Snackbar.Add($"Greška: {error.Description}", Severity.Error)
        );
    }

    private RenderFragment RenderBusSeats(TripBusDto bus, TripBusDirection direction) => __builder =>
    {
        var capacity = bus.Bus?.Capacity ?? 50;
        var seatsPerRow = 4;
        var rows = (int)Math.Ceiling((double)capacity / seatsPerRow);

        <div class="bus-layout">
            @for (int row = 0; row < rows; row++)
            {
                <div class="seat-row" style="display: flex; justify-content: center; margin-bottom: 8px;">
                    @for (int col = 0; col < seatsPerRow; col++)
                    {
                        var seatNumber = row * seatsPerRow + col + 1;
                        if (seatNumber <= capacity)
                        {
                            var isOccupied = IsSeatOccupied(bus, seatNumber, direction);
                            var travelerInSeat = GetTravelerInSeat(bus, seatNumber, direction);
                            var seatClass = isOccupied ? "seat occupied" : "seat available";

                            <div class="@seatClass"
                                 style="width: 40px; height: 40px; margin: 2px; border: 2px solid #ccc; border-radius: 4px; display: flex; align-items: center; justify-content: center; cursor: pointer; background-color: @(isOccupied ? "#f44336" : "#4caf50"); color: white; font-weight: bold;"
                                 @onclick="@(() => AssignSeat(bus, seatNumber, direction))"
                                 title="@(travelerInSeat?.Person?.FullName ?? $"Sjedište {seatNumber}")">
                                @seatNumber
                            </div>
                        }

                        @if (col == 1)
                        {
                            <div style="width: 20px;"></div> <!-- Aisle space -->
                        }
                    }
                </div>
            }
        </div>
    };
}
