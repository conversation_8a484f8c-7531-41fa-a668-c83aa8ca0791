@page "/trips/{TripId:guid}/seat-assignment"
@using OdmoriBa.Application.Features.Travelers.Models
@using OdmoriBa.Application.Features.Trips.Commands
@using OdmoriBa.Application.Features.Trips.Models
@using OdmoriBa.Core.Domains.Trips.Entities
@using OdmoriBa.Web.Components.Shared

@implements IDisposable

@inject IDialogService DialogService
@inject TripState TripState
@inject IMediator Mediator
@inject ISnackbar Snackbar
@inject IJSRuntime JSRuntime

<PageTitle>Putovanje | Raspored sjedišta</PageTitle>

<MudToolBar Class="d-flex flex-wrap">
    <MudBreadcrumbs
        Items="@(
               [
                   new BreadcrumbItem("Putovanja", href: "/trips", icon: Icons.Material.Filled.CardTravel),
                   new BreadcrumbItem($"{_trip?.Title} ({_trip?.DateRange})", href: $"/trips/{_trip?.Id!}", icon: _trip?.TransportationType == TransportationType.Bus ? Icons.Material.Filled.DirectionsBus : Icons.Material.Filled.AirplanemodeActive),
                   new BreadcrumbItem("Raspored sjedišta", href: $"/trips/{_trip?.Id!}/seat-assignment", icon: Icons.Material.Filled.EventSeat),
               ])">
    </MudBreadcrumbs>
    <MudSpacer/>

    <MudButton StartIcon="@Icons.Material.Filled.Save"
               OnClick="@SaveSeatAssignmentsAsync"
               Variant="Variant.Filled"
               Color="Color.Primary"
               Disabled="@(!_hasChanges)">
        Sačuvaj raspored
    </MudButton>
</MudToolBar>

<MudTabs Elevation="2" Rounded="true" ApplyEffectsToContainer="true" PanelClass="pa-6">
    <MudTabPanel Text="Polazak" Icon="@Icons.Material.Filled.FlightTakeoff">
        @if (_trip?.TripBuses.Any(b => b.Direction == TripBusDirection.Departure) == true)
        {
            <MudGrid>
                <MudItem md="3">
                    <MudPaper Class="pa-3" Elevation="2">
                        <div class="d-flex align-center justify-space-between mb-3">
                            <MudText Typo="Typo.h6">Putnici</MudText>
                            <MudChip T="string" Size="Size.Small" Color="Color.Info">
                                @GetUnassignedCount(TripBusDirection.Departure) / @GetTotalTravelersForDirection(TripBusDirection.Departure) preostalo
                            </MudChip>
                        </div>

                        <div class="d-flex gap-2 mb-3">
                            <MudButton Size="Size.Small"
                                      Variant="Variant.Filled"
                                      Color="Color.Success"
                                      StartIcon="@Icons.Material.Filled.AutoAwesome"
                                      OnClick="@(() => AutoAssignAll(TripBusDirection.Departure))"
                                      Disabled="@(GetUnassignedCount(TripBusDirection.Departure) == 0)">
                                Auto raspored
                            </MudButton>
                            <MudButton Size="Size.Small"
                                      Variant="Variant.Outlined"
                                      Color="Color.Error"
                                      StartIcon="@Icons.Material.Filled.Clear"
                                      OnClick="@(() => ClearAllAssignments(TripBusDirection.Departure))"
                                      Disabled="@(GetAssignedCount(TripBusDirection.Departure) == 0)">
                                Ukloni sve
                            </MudButton>
                        </div>
                        @foreach (var travelParty in GetTravelPartiesWithTravelers(TripBusDirection.Departure))
                        {
                            <MudCard Class="mb-2" Elevation="1">
                                <MudCardContent Class="pa-2">
                                    <div class="d-flex align-center justify-space-between mb-2">
                                        <div>
                                            <MudText Typo="Typo.subtitle2">@travelParty.MainContact?.FullName</MudText>
                                            <MudText Typo="Typo.caption" Class="mud-text-secondary">
                                                @travelParty.Travelers.Count putnika (@GetUnassignedTravelers(travelParty, TripBusDirection.Departure).Count neraspoređeno)
                                            </MudText>
                                        </div>
                                        @if (GetUnassignedTravelers(travelParty, TripBusDirection.Departure).Any())
                                        {
                                            <MudButton Size="Size.Small"
                                                      Variant="Variant.Outlined"
                                                      Color="Color.Primary"
                                                      OnClick="@(() => SelectTravelParty(travelParty, TripBusDirection.Departure))">
                                                Odaberi grupu
                                            </MudButton>
                                        }
                                    </div>
                                    @foreach (var traveler in travelParty.Travelers)
                                    {
                                        var isAssigned = IsTravelerAssigned(traveler, TripBusDirection.Departure);
                                        var isSelected = IsSelected(traveler, TripBusDirection.Departure);
                                        <MudChip T="string" Size="Size.Small"
                                                Class="@($"ma-1 {(isSelected ? "mud-theme-primary" : "")}")"
                                                Style="@($"cursor: {(isAssigned ? "default" : "pointer")}; opacity: {(isAssigned ? "0.6" : "1")}")"
                                                Disabled="@isAssigned"
                                                @onclick="@(() => { if (!isAssigned) SelectTraveler(traveler, TripBusDirection.Departure); })">
                                            @traveler.Person?.FullName
                                            @if (isAssigned)
                                            {
                                                <MudIcon Icon="@Icons.Material.Filled.Check" Size="Size.Small" Class="ml-1" />
                                            }
                                        </MudChip>
                                    }
                                </MudCardContent>
                            </MudCard>
                        }
                    </MudPaper>
                </MudItem>
                <MudItem md="9">
                    <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true">
                        @foreach (var bus in _trip.TripBuses.Where(b => b.Direction == TripBusDirection.Departure))
                        {
                            <MudTabPanel Text="@bus.Name" Icon="@Icons.Material.Filled.DirectionsBus">
                                <div class="pa-4">
                                    <MudText Typo="Typo.h6" Class="mb-4">@bus.Bus?.FullName</MudText>
                                    @RenderBusSeats(bus, TripBusDirection.Departure)
                                </div>
                            </MudTabPanel>
                        }
                    </MudTabs>
                </MudItem>
            </MudGrid>
        }
        else
        {
            <MudAlert Severity="Severity.Info">Nema autobusa za polazak.</MudAlert>
        }
    </MudTabPanel>

    <MudTabPanel Text="Povratak" Icon="@Icons.Material.Filled.FlightLand">
        @if (_trip?.TripBuses.Any(b => b.Direction == TripBusDirection.Return) == true)
        {
            <MudGrid>
                <MudItem md="3">
                    <MudPaper Class="pa-3" Elevation="2">
                        <div class="d-flex align-center justify-space-between mb-3">
                            <MudText Typo="Typo.h6">Putnici</MudText>
                            <MudChip T="string" Size="Size.Small" Color="Color.Info">
                                @GetUnassignedCount(TripBusDirection.Return) / @GetTotalTravelersForDirection(TripBusDirection.Return) preostalo
                            </MudChip>
                        </div>

                        <div class="d-flex gap-2 mb-3">
                            <MudButton Size="Size.Small"
                                      Variant="Variant.Filled"
                                      Color="Color.Success"
                                      StartIcon="@Icons.Material.Filled.AutoAwesome"
                                      OnClick="@(() => AutoAssignAll(TripBusDirection.Return))"
                                      Disabled="@(GetUnassignedCount(TripBusDirection.Return) == 0)">
                                Auto raspored
                            </MudButton>
                            <MudButton Size="Size.Small"
                                      Variant="Variant.Outlined"
                                      Color="Color.Error"
                                      StartIcon="@Icons.Material.Filled.Clear"
                                      OnClick="@(() => ClearAllAssignments(TripBusDirection.Return))"
                                      Disabled="@(GetAssignedCount(TripBusDirection.Return) == 0)">
                                Ukloni sve
                            </MudButton>
                        </div>

                        @foreach (var travelParty in GetTravelPartiesWithTravelers(TripBusDirection.Return))
                        {
                            <MudCard Class="mb-2" Elevation="1">
                                <MudCardContent Class="pa-2">
                                    <div class="d-flex align-center justify-space-between mb-2">
                                        <div>
                                            <MudText Typo="Typo.subtitle2">@travelParty.MainContact?.FullName</MudText>
                                            <MudText Typo="Typo.caption" Class="mud-text-secondary">
                                                @travelParty.Travelers.Count putnika (@GetUnassignedTravelers(travelParty, TripBusDirection.Return).Count neraspoređeno)
                                            </MudText>
                                        </div>
                                        @if (GetUnassignedTravelers(travelParty, TripBusDirection.Return).Any())
                                        {
                                            <MudButton Size="Size.Small"
                                                      Variant="Variant.Outlined"
                                                      Color="Color.Primary"
                                                      OnClick="@(() => SelectTravelParty(travelParty, TripBusDirection.Return))">
                                                Odaberi grupu
                                            </MudButton>
                                        }
                                    </div>
                                    @foreach (var traveler in travelParty.Travelers)
                                    {
                                        var isAssigned = IsTravelerAssigned(traveler, TripBusDirection.Return);
                                        var isSelected = IsSelected(traveler, TripBusDirection.Return);
                                        <MudChip T="string" Size="Size.Small"
                                                Class="@($"ma-1 {(isSelected ? "mud-theme-primary" : "")}")"
                                                Style="@($"cursor: {(isAssigned ? "default" : "pointer")}; opacity: {(isAssigned ? "0.6" : "1")}")"
                                                Disabled="@isAssigned"
                                                @onclick="@(() => { if (!isAssigned) SelectTraveler(traveler, TripBusDirection.Return); })">
                                            @traveler.Person?.FullName
                                            @if (isAssigned)
                                            {
                                                <MudIcon Icon="@Icons.Material.Filled.Check" Size="Size.Small" Class="ml-1" />
                                            }
                                        </MudChip>
                                    }
                                </MudCardContent>
                            </MudCard>
                        }
                    </MudPaper>
                </MudItem>
                <MudItem md="9">
                    <MudTabs Elevation="1" Rounded="true" ApplyEffectsToContainer="true">
                        @foreach (var bus in _trip.TripBuses.Where(b => b.Direction == TripBusDirection.Return))
                        {
                            <MudTabPanel Text="@bus.Name" Icon="@Icons.Material.Filled.DirectionsBus">
                                <div class="pa-4">
                                    <MudText Typo="Typo.h6" Class="mb-4">@bus.Bus?.FullName</MudText>
                                    @RenderBusSeats(bus, TripBusDirection.Return)
                                </div>
                            </MudTabPanel>
                        }
                    </MudTabs>
                </MudItem>
            </MudGrid>
        }
        else
        {
            <MudAlert Severity="Severity.Info">Nema autobusa za povratak.</MudAlert>
        }
    </MudTabPanel>
</MudTabs>

@code {
    [Parameter] public Guid TripId { get; set; }

    private TripDto? _trip = new();
    private List<TravelPartyDto> _travelParties = [];
    private List<TravelerDto> _selectedTravelers = [];
    private TripBusDirection _currentDirection = TripBusDirection.Departure;
    private bool _hasChanges = false;

    protected override async Task OnParametersSetAsync()
    {
        _trip = await TripState.LoadTripAsync(TripId);
        _travelParties = await TripState.LoadTravelPartiesAsync(TripId);
    }

    protected override void OnInitialized()
    {
        TripState.OnChange += StateHasChanged;
    }

    public void Dispose()
    {
        TripState.OnChange -= StateHasChanged;
    }

    private void SelectTraveler(TravelerDto traveler, TripBusDirection direction)
    {
        _currentDirection = direction;
        _selectedTravelers.Clear();
        _selectedTravelers.Add(traveler);
        StateHasChanged();
    }

    private void SelectTravelParty(TravelPartyDto travelParty, TripBusDirection direction)
    {
        _currentDirection = direction;
        _selectedTravelers.Clear();
        _selectedTravelers.AddRange(GetUnassignedTravelers(travelParty, direction));
        StateHasChanged();
    }

    private bool IsSelected(TravelerDto traveler, TripBusDirection direction)
    {
        return _currentDirection == direction && _selectedTravelers.Contains(traveler);
    }

    private string GetBusName(Guid busId)
    {
        var bus = _trip?.TripBuses.FirstOrDefault(b => b.Id == busId);
        return bus?.Name ?? "Nepoznat autobus";
    }

    private void AssignSeat(TripBusDto bus, int seatNumber, TripBusDirection direction)
    {
        if (!_selectedTravelers.Any()) return;

        // If seat is occupied, don't assign
        if (IsSeatOccupied(bus, seatNumber, direction)) return;

        // If multiple travelers selected (group), assign to consecutive available seats
        if (_selectedTravelers.Count > 1)
        {
            AssignGroupToConsecutiveSeats(bus, seatNumber, direction);
        }
        else
        {
            // Single traveler assignment
            var traveler = _selectedTravelers.First();
            AssignTravelerToSeat(traveler, bus, seatNumber, direction);
            _selectedTravelers.Clear();
        }

        _hasChanges = true;
        StateHasChanged();
    }

    private void AssignGroupToConsecutiveSeats(TripBusDto bus, int startSeatNumber, TripBusDirection direction)
    {
        var capacity = bus.Bus?.Capacity ?? 50;
        var currentSeat = startSeatNumber;
        var travelersToAssign = _selectedTravelers.ToList();

        foreach (var traveler in travelersToAssign)
        {
            // Find next available seat starting from currentSeat
            while (currentSeat <= capacity && IsSeatOccupied(bus, currentSeat, direction))
            {
                currentSeat++;
            }

            if (currentSeat <= capacity)
            {
                AssignTravelerToSeat(traveler, bus, currentSeat, direction);
                currentSeat++;
            }
        }

        _selectedTravelers.Clear();
    }

    private void AssignTravelerToSeat(TravelerDto traveler, TripBusDto bus, int seatNumber, TripBusDirection direction)
    {
        if (direction == TripBusDirection.Departure)
        {
            traveler.DepartureTripBusId = bus.Id;
            traveler.DepartureSeatNumber = seatNumber;
        }
        else
        {
            traveler.ReturnTripBusId = bus.Id;
            traveler.ReturnSeatNumber = seatNumber;
        }
    }

    private bool IsSeatOccupied(TripBusDto bus, int seatNumber, TripBusDirection direction)
    {
        var allTravelers = _travelParties.SelectMany(tp => tp.Travelers);

        if (direction == TripBusDirection.Departure)
        {
            return allTravelers.Any(t => t.DepartureTripBusId == bus.Id && t.DepartureSeatNumber == seatNumber);
        }
        else
        {
            return allTravelers.Any(t => t.ReturnTripBusId == bus.Id && t.ReturnSeatNumber == seatNumber);
        }
    }

    private TravelerDto? GetTravelerInSeat(TripBusDto bus, int seatNumber, TripBusDirection direction)
    {
        var allTravelers = _travelParties.SelectMany(tp => tp.Travelers);

        if (direction == TripBusDirection.Departure)
        {
            return allTravelers.FirstOrDefault(t => t.DepartureTripBusId == bus.Id && t.DepartureSeatNumber == seatNumber);
        }
        else
        {
            return allTravelers.FirstOrDefault(t => t.ReturnTripBusId == bus.Id && t.ReturnSeatNumber == seatNumber);
        }
    }

    private async Task SaveSeatAssignmentsAsync()
    {
        var assignments = new List<TravelerSeatAssignment>();

        foreach (var travelParty in _travelParties)
        {
            foreach (var traveler in travelParty.Travelers)
            {
                assignments.Add(new TravelerSeatAssignment(
                    traveler.Id,
                    traveler.TravelPartyId,
                    traveler.DepartureTripBusId,
                    traveler.DepartureSeatNumber,
                    traveler.ReturnTripBusId,
                    traveler.ReturnSeatNumber));
            }
        }

        var command = new UpdateTravelersSeatAssignmentsCommand(assignments);
        var result = await Mediator.Send(command);

        result.Switch(
            () =>
            {
                Snackbar.Add("Raspored sjedišta je uspješno sačuvan!", Severity.Success);
                _hasChanges = false;
            },
            error => Snackbar.Add($"Greška: {error.Description}", Severity.Error)
        );
    }

    private RenderFragment RenderBusSeats(TripBusDto bus, TripBusDirection direction) => __builder =>
    {
        var capacity = bus.Bus?.Capacity ?? 50;
        var seatsPerRow = 4;
        var rows = (int)Math.Ceiling((double)capacity / seatsPerRow);

        <div class="bus-layout">
            @for (int row = 0; row < rows; row++)
            {
                <div class="seat-row" style="display: flex; justify-content: center; margin-bottom: 16px;">
                    @for (int col = 0; col < seatsPerRow; col++)
                    {
                        var seatNumber = row * seatsPerRow + col + 1;
                        if (seatNumber <= capacity)
                        {
                            var isOccupied = IsSeatOccupied(bus, seatNumber, direction);
                            var travelerInSeat = GetTravelerInSeat(bus, seatNumber, direction);
                            var seatClass = isOccupied ? "seat occupied" : "seat available";
                            var travelerName = travelerInSeat?.Person?.FullName;
                            var displayName = !string.IsNullOrEmpty(travelerName) && travelerName.Length > 15
                                ? travelerName.Substring(0, 15) + "..."
                                : travelerName;
                            var travelPartyColor = GetTravelPartyColor(travelerInSeat?.TravelPartyId);
                            var seatId = $"seat-{bus.Id}-{seatNumber}-{direction}";

                            @if (isOccupied && travelerInSeat != null)
                            {
                                <MudTooltip>
                                    <ChildContent>
                                        <div id="@seatId" class="@seatClass seat-container"
                                             style="position: relative; width: 100px; height: 80px; margin: 6px; border: 3px solid #ccc; border-radius: 12px; display: flex; flex-direction: column; align-items: center; justify-content: center; cursor: pointer; background-color: @(isOccupied ? "#f44336" : "#4caf50"); color: white; font-weight: bold; font-size: 11px; text-align: center; padding: 4px; transition: all 0.2s ease;"
                                             @onclick="@(() => AssignSeat(bus, seatNumber, direction))"
                                             @onmouseenter="@(() => ShowRemoveButton(seatId, isOccupied))"
                                             @onmouseleave="@(() => HideRemoveButton(seatId))">

                                            @if (isOccupied && !string.IsNullOrEmpty(travelPartyColor))
                                            {
                                                <div style="position: absolute; top: 0; left: 0; right: 0; height: 8px; background: @travelPartyColor; border-radius: 8px 8px 0 0;"></div>
                                            }

                                            <button id="@($"{seatId}-remove")"
                                                    class="remove-btn"
                                                    style="position: absolute; top: -8px; right: -8px; width: 20px; height: 20px; border-radius: 50%; background: #ff1744; color: white; border: none; font-size: 12px; font-weight: bold; cursor: pointer; display: none; z-index: 10; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"
                                                    @onclick="@(() => RemoveTravelerFromSeat(travelerInSeat, direction))"
                                                    @onclick:stopPropagation="true"
                                                    title="Ukloni putnika sa sjedišta">
                                                ×
                                            </button>

                                            <div style="font-size: 14px; font-weight: bold; margin-top: @(isOccupied && !string.IsNullOrEmpty(travelPartyColor) ? "8px" : "0");">@seatNumber</div>
                                            @if (!string.IsNullOrEmpty(displayName))
                                            {
                                                <div style="font-size: 10px; line-height: 1.2; word-break: break-word; margin-top: 2px;">@displayName</div>
                                            }
                                        </div>
                                    </ChildContent>
                                    <TooltipContent>
                                        <TravelerTooltip Traveler="@travelerInSeat" TravelParty="@GetTravelPartyForTraveler(travelerInSeat)" />
                                    </TooltipContent>
                                </MudTooltip>
                            }
                            else
                            {
                                <div id="@seatId" class="@seatClass seat-container"
                                     style="position: relative; width: 100px; height: 80px; margin: 6px; border: 3px solid #ccc; border-radius: 12px; display: flex; flex-direction: column; align-items: center; justify-content: center; cursor: pointer; background-color: @(isOccupied ? "#f44336" : "#4caf50"); color: white; font-weight: bold; font-size: 11px; text-align: center; padding: 4px; transition: all 0.2s ease;"
                                     @onclick="@(() => AssignSeat(bus, seatNumber, direction))"
                                     title="@($"Sjedište {seatNumber}")">

                                    <div style="font-size: 14px; font-weight: bold;">@seatNumber</div>
                                </div>
                            }
                        }

                        @if (col == 1)
                        {
                            <div style="width: 40px;"></div> <!-- Aisle space -->
                        }
                    }
                </div>
            }
        </div>
    };

    private List<TravelPartyDto> GetUnassignedTravelParties(TripBusDirection direction)
    {
        return _travelParties.Where(tp => GetUnassignedTravelers(tp, direction).Any()).ToList();
    }

    private List<TravelPartyDto> GetTravelPartiesWithTravelers(TripBusDirection direction)
    {
        // Show travel parties that have at least one unassigned traveler for this direction
        return _travelParties.Where(tp => GetUnassignedTravelers(tp, direction).Any()).ToList();
    }

    private List<TravelerDto> GetUnassignedTravelers(TravelPartyDto travelParty, TripBusDirection direction)
    {
        return travelParty.Travelers.Where(t => !IsTravelerAssigned(t, direction)).ToList();
    }

    private bool IsTravelerAssigned(TravelerDto traveler, TripBusDirection direction)
    {
        if (direction == TripBusDirection.Departure)
        {
            return traveler.DepartureTripBusId.HasValue && traveler.DepartureSeatNumber.HasValue;
        }
        else
        {
            return traveler.ReturnTripBusId.HasValue && traveler.ReturnSeatNumber.HasValue;
        }
    }

    private int GetUnassignedCount(TripBusDirection direction)
    {
        var totalTravelers = GetTotalTravelersForDirection(direction);
        var assignedTravelers = GetAssignedCount(direction);
        return totalTravelers - assignedTravelers;
    }

    private int GetTotalTravelersForDirection(TripBusDirection direction)
    {
        // Count all travelers that should be on this direction
        // For departure: all travelers
        // For return: travelers who have return trip buses available or are already assigned to return
        return _travelParties.SelectMany(tp => tp.Travelers)
            .Count(t => direction == TripBusDirection.Departure ||
                       t.ReturnTripBusId.HasValue ||
                       _trip?.TripBuses.Any(b => b.Direction == TripBusDirection.Return) == true);
    }

    private int GetAssignedCount(TripBusDirection direction)
    {
        return _travelParties.SelectMany(tp => tp.Travelers)
            .Count(t => IsTravelerAssigned(t, direction));
    }

    private string GetTravelPartyColor(Guid? travelPartyId)
    {
        if (!travelPartyId.HasValue) return "";

        // Get the index of this travel party in the ordered list
        var travelPartyIndex = GetTravelPartyIndex(travelPartyId.Value);

        // Alternating colors for travel parties
        var colors = new[]
        {
            "#2196F3", // Blue
            "#4CAF50", // Green
            "#FF9800", // Orange
            "#9C27B0", // Purple
            "#F44336", // Red
            "#00BCD4", // Cyan
            "#795548", // Brown
            "#607D8B"  // Blue Grey
        };

        return colors[travelPartyIndex % colors.Length];
    }

    private int GetTravelPartyIndex(Guid travelPartyId)
    {
        // Get all unique travel party IDs in a consistent order
        var uniqueTravelPartyIds = _travelParties
            .Select(tp => tp.Id)
            .Distinct()
            .OrderBy(id => id)
            .ToList();

        return uniqueTravelPartyIds.IndexOf(travelPartyId);
    }

    private void RemoveTravelerFromSeat(TravelerDto? traveler, TripBusDirection direction)
    {
        if (traveler == null) return;

        if (direction == TripBusDirection.Departure)
        {
            traveler.DepartureTripBusId = null;
            traveler.DepartureSeatNumber = null;
        }
        else
        {
            traveler.ReturnTripBusId = null;
            traveler.ReturnSeatNumber = null;
        }

        _hasChanges = true;
        StateHasChanged();
    }

    private async Task ShowRemoveButton(string seatId, bool isOccupied)
    {
        if (isOccupied)
        {
            await JSRuntime.InvokeVoidAsync("showRemoveButton", $"{seatId}-remove");
        }
    }

    private async Task HideRemoveButton(string seatId)
    {
        await JSRuntime.InvokeVoidAsync("hideRemoveButton", $"{seatId}-remove");
    }

    private TravelPartyDto? GetTravelPartyForTraveler(TravelerDto traveler)
    {
        return _travelParties.FirstOrDefault(tp => tp.Id == traveler.TravelPartyId);
    }

    private void AutoAssignAll(TripBusDirection direction)
    {
        var buses = _trip?.TripBuses.Where(b => b.Direction == direction).OrderBy(b => b.Name).ToList();
        if (buses == null || !buses.Any()) return;

        var unassignedTravelers = _travelParties
            .SelectMany(tp => tp.Travelers)
            .Where(t => !IsTravelerAssigned(t, direction))
            .ToList();

        if (!unassignedTravelers.Any()) return;

        var currentBusIndex = 0;
        var currentSeat = 1;

        foreach (var traveler in unassignedTravelers)
        {
            if (currentBusIndex >= buses.Count()) break;

            var currentBus = buses[currentBusIndex];
            var capacity = currentBus.Bus?.Capacity ?? 50;

            // Find next available seat in current bus
            while (currentSeat <= capacity && IsSeatOccupied(currentBus, currentSeat, direction))
            {
                currentSeat++;
            }

            // If current bus is full, move to next bus
            if (currentSeat > capacity)
            {
                currentBusIndex++;
                currentSeat = 1;

                if (currentBusIndex >= buses.Count()) break;

                currentBus = buses[currentBusIndex];
                capacity = currentBus.Bus?.Capacity ?? 50;

                // Find first available seat in new bus
                while (currentSeat <= capacity && IsSeatOccupied(currentBus, currentSeat, direction))
                {
                    currentSeat++;
                }

                if (currentSeat > capacity) break;
            }

            // Assign traveler to seat
            AssignTravelerToSeat(traveler, currentBus, currentSeat, direction);
            currentSeat++;
        }

        _hasChanges = true;
        StateHasChanged();
    }

    private void ClearAllAssignments(TripBusDirection direction)
    {
        var assignedTravelers = _travelParties
            .SelectMany(tp => tp.Travelers)
            .Where(t => IsTravelerAssigned(t, direction))
            .ToList();

        foreach (var traveler in assignedTravelers)
        {
            if (direction == TripBusDirection.Departure)
            {
                traveler.DepartureTripBusId = null;
                traveler.DepartureSeatNumber = null;
            }
            else
            {
                traveler.ReturnTripBusId = null;
                traveler.ReturnSeatNumber = null;
            }
        }

        _selectedTravelers.Clear();
        _hasChanges = true;
        StateHasChanged();
    }
}

<script>
    window.showRemoveButton = (buttonId) => {
        const button = document.getElementById(buttonId);
        if (button) {
            button.style.display = 'flex';
            button.style.alignItems = 'center';
            button.style.justifyContent = 'center';
        }
    };

    window.hideRemoveButton = (buttonId) => {
        const button = document.getElementById(buttonId);
        if (button) {
            button.style.display = 'none';
        }
    };
</script>
