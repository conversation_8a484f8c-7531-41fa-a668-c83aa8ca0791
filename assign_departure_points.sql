-- Assign departure points to travelers with better distribution
-- All travelers in the same travel party will get the same departure point

WITH trip_stops_available AS (
  SELECT
    id as trip_stop_id,
    row_number() OVER (ORDER BY id) as stop_number
  FROM trip_stops
  WHERE trip_id = '01985a39-dd62-7976-a44b-f22b46f36bb7'
),
travel_parties_numbered AS (
  SELECT
    tp.id as travel_party_id,
    row_number() OVER (ORDER BY random()) as party_number
  FROM travel_parties tp
  WHERE tp.trip_id = '01985a39-dd62-7976-a44b-f22b46f36bb7'
),
travel_parties_with_stops AS (
  SELECT
    tpn.travel_party_id,
    -- Distribute parties across available stops
    (SELECT trip_stop_id FROM trip_stops_available
     WHERE stop_number = ((tpn.party_number - 1) % (SELECT COUNT(*) FROM trip_stops_available)) + 1
    ) as assigned_departure_point_id,
    -- Assign different return stops randomly
    (SELECT trip_stop_id FROM trip_stops_available
     WHERE stop_number = ((tpn.party_number + 1) % (SELECT COUNT(*) FROM trip_stops_available)) + 1
    ) as assigned_return_departure_point_id
  FROM travel_parties_numbered tpn
)
UPDATE travelers
SET
  departure_point_id = tpws.assigned_departure_point_id,
  return_departure_point_id = tpws.assigned_return_departure_point_id,
  updated_at = now()
FROM travel_parties_with_stops tpws
WHERE travelers.travel_party_id = tpws.travel_party_id;
